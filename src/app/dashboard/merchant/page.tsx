"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Plus, Storefront, CurrencyDollar, User, House, CaretRight, QrCode, ChartBar, ShoppingBag, Gear } from "phosphor-react";
import Link from "next/link";
import { useGetMerchantShopsQuery, useGetMerchantCreditStatsQuery } from "@/lib/api/apiSlice";
import { MerchantShop } from "@/types";

export default function MerchantDashboardPage() {
  const { data: shops, isLoading: isShopsLoading } = useGetMerchantShopsQuery();
  const {
    data: creditStats,
    isLoading: isStatsLoading
  } = useGetMerchantCreditStatsQuery();
  const router = useRouter();

  // Get credit statistics from API
  const totalCreditsIssued = creditStats?.total_credits_issued || 0;
  const totalCreditsRedeemed = creditStats?.total_credits_redeemed || 0;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background justify-between group/design-root overflow-x-hidden">
      <div className="flex-1">
        {/* Header - Mobile & Desktop */}
        <div className="flex items-center bg-background p-4 pb-2 justify-between border-b">
          <h2 className="text-foreground text-lg md:text-2xl font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12 md:pl-0">Shop Dashboard</h2>
          <div className="flex w-12 items-center justify-end">
            <Link href="/dashboard/merchant/new">
              <Button
                className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0 md:px-4 md:py-2"
                variant="ghost"
              >
                <Plus size={24} weight="regular" />
                <span className="hidden md:inline">New Shop</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Main Content - Responsive Layout */}
        <div className="container py-4 md:py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6 md:mb-8">
            <Card>
              <CardHeader className="pb-2 space-y-1">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <CurrencyDollar className="h-4 w-4 text-primary" />
                    Total Credits Issued
                  </CardTitle>
                  <Badge variant="outline" className="text-xs font-normal">
                    Issued
                  </Badge>
                </div>
                <CardDescription>Credits distributed to customers</CardDescription>
              </CardHeader>
              <CardContent>
                {isStatsLoading ? (
                  <Skeleton className="h-10 w-32" />
                ) : (
                  <div className="text-2xl md:text-3xl font-bold">
                    ${totalCreditsIssued.toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2 space-y-1">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <QrCode className="h-4 w-4 text-primary" />
                    Total Credits Redeemed
                  </CardTitle>
                  <Badge variant="outline" className="text-xs font-normal">
                    Redeemed
                  </Badge>
                </div>
                <CardDescription>Credits used by customers</CardDescription>
              </CardHeader>
              <CardContent>
                {isStatsLoading ? (
                  <Skeleton className="h-10 w-32" />
                ) : (
                  <div className="text-2xl md:text-3xl font-bold">
                    ${totalCreditsRedeemed.toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Tabs for different sections */}
          <Tabs defaultValue="shops" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="shops" className="text-sm py-2">
                <Storefront className="mr-2 h-4 w-4" />
                My Shops
              </TabsTrigger>
              <TabsTrigger value="quick-actions" className="text-sm py-2">
                <CurrencyDollar className="mr-2 h-4 w-4" />
                Quick Actions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="shops" className="space-y-4">
              <Card>
                <CardHeader className="space-y-1">
                  <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
                    <Storefront className="h-4 w-4 text-primary" />
                    My Shops
                  </CardTitle>
                  <CardDescription>Manage your merchant shops</CardDescription>
                </CardHeader>
                <CardContent>
                  {isShopsLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
                          <Skeleton className="h-14 w-14 rounded-lg" />
                          <div className="flex-1">
                            <Skeleton className="h-5 w-32 mb-2" />
                            <Skeleton className="h-4 w-48" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : shops && shops.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {shops.map((shop: MerchantShop) => (
                        <Link href={`/dashboard/merchant/shop/${shop.slug}`} key={shop.id} className="block">
                          <Card className="hover:shadow-md transition-shadow overflow-hidden h-full border-border">
                            <div className="flex items-center gap-4 p-4">
                              <div className="bg-muted rounded-lg size-14 flex items-center justify-center flex-shrink-0">
                                <Storefront size={32} weight="regular" className="text-muted-foreground" />
                              </div>
                              <div className="flex flex-col justify-center flex-1 min-w-0">
                                <p className="text-foreground text-base font-medium leading-normal line-clamp-1">{shop.name}</p>
                                <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2">{shop.description || "No description"}</p>
                              </div>
                              <div className="shrink-0">
                                <div className="text-foreground flex size-7 items-center justify-center">
                                  <CaretRight size={24} weight="regular" />
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <Storefront size={48} weight="regular" className="text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground mb-4">You don&apos;t have any shops yet.</p>
                      <Button
                        onClick={() => router.push("/dashboard/merchant/new")}
                      >
                        Create Your First Shop
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="quick-actions" className="space-y-4">
              <Card>
                <CardHeader className="space-y-1">
                  <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
                    <CurrencyDollar className="h-4 w-4 text-primary" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>Common tasks and shortcuts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="overflow-hidden hover:shadow-md transition-shadow border-border">
                      <Link href="/dashboard/merchant/credits" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-primary/10 rounded-full p-3">
                            <CurrencyDollar size={24} className="text-primary" />
                          </div>
                          <div>
                            <h3 className="font-medium text-foreground">Manage Credits</h3>
                            <p className="text-sm text-muted-foreground">View and manage customer credits</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow border-border">
                      <Link href="/dashboard/merchant/codes" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-primary/10 rounded-full p-3">
                            <QrCode size={24} className="text-primary" />
                          </div>
                          <div>
                            <h3 className="font-medium text-foreground">Credit Codes</h3>
                            <p className="text-sm text-muted-foreground">Generate and manage credit codes</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow border-border">
                      <Link href="/dashboard/merchant/new" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-primary/10 rounded-full p-3">
                            <Plus size={24} className="text-primary" />
                          </div>
                          <div>
                            <h3 className="font-medium text-foreground">Create Shop</h3>
                            <p className="text-sm text-muted-foreground">Add a new merchant shop</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow border-border">
                      <Link href="/dashboard/merchant/profile" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-primary/10 rounded-full p-3">
                            <User size={24} className="text-primary" />
                          </div>
                          <div>
                            <h3 className="font-medium text-foreground">Profile</h3>
                            <p className="text-sm text-muted-foreground">View and edit your profile</p>
                          </div>
                        </div>
                      </Link>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Mobile Navigation - Hidden on Desktop */}
      <div className="md:hidden">
        <div className="flex gap-2 border-t border-border bg-background px-4 pb-3 pt-2">
          <Link className="flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-foreground" href="/dashboard/merchant">
            <div className="text-foreground flex h-8 items-center justify-center">
              <House size={24} weight="fill" />
            </div>
            <p className="text-foreground text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </Link>
          <Link className="flex flex-1 flex-col items-center justify-end gap-1 text-muted-foreground" href="/dashboard/merchant/credits">
            <div className="text-muted-foreground flex h-8 items-center justify-center">
              <CurrencyDollar size={24} weight="regular" />
            </div>
            <p className="text-muted-foreground text-xs font-medium leading-normal tracking-[0.015em]">Credits</p>
          </Link>
          <Link className="flex flex-1 flex-col items-center justify-end gap-1 text-muted-foreground" href="/dashboard/merchant/codes">
            <div className="text-muted-foreground flex h-8 items-center justify-center">
              <QrCode size={24} weight="regular" />
            </div>
            <p className="text-muted-foreground text-xs font-medium leading-normal tracking-[0.015em]">Codes</p>
          </Link>
          <Link className="flex flex-1 flex-col items-center justify-end gap-1 text-muted-foreground" href="/dashboard/merchant/profile">
            <div className="text-muted-foreground flex h-8 items-center justify-center">
              <User size={24} weight="regular" />
            </div>
            <p className="text-muted-foreground text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </Link>
        </div>
        <div className="h-5 bg-background"></div>
      </div>
    </div>
  );
}
